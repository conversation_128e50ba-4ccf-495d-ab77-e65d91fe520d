import config
from dotenv import load_dotenv
from Fyers import Fyers_Utility 
import logger_config 
from Symphony import Fyers_To_Symphony_Converter 
from Symphony import Symphony_Get_Masterdf 
from Symphony import Symphony_Get_Order_Book 
from Symphony import Symphony_Utility
import time, os

#Get logger
#logger = setup_logger('main_logger', 'main.log')

logger = logger_config.get_logger('main_logger')
broker = config.BrokerContext()
# Load environment variables from .env file
load_dotenv()

def adjusted_limit_price(price, side):
    logger.info(f"price and side, {price}, {side}")
    if price <= 2:
        logger.info(f"price is less than 2")
        if side == "buy":
            price += 0.10
        else:
            price -= 0.10
        
        # Find the nearest 0.05 multiple
        limitAdjustedPrice = round(price / 0.05) * 0.05
        limitAdjustedPrice = max(0.05,limitAdjustedPrice)
        logger.info(f"limitAdjustedPrice:, {limitAdjustedPrice:.2f}")
        return round(limitAdjustedPrice, 2)
    else:
        logger.info(f"price is greater than or equal to 2")
        if side == "buy":
            buffer = 1.04
        else:
            buffer = 0.96
        
        # Apply buffer and find the nearest 0.05 multiple
        limitAdjustedPrice = round((price * buffer) / 0.05) * 0.05
        limitAdjustedPrice = max(0.05,limitAdjustedPrice)
        logger.info(f"limitAdjustedPrice:, {limitAdjustedPrice:.2f}")

    # Ensure the result is rounded to two decimal places for clarity
    return round(limitAdjustedPrice, 2)

def symphony_place_order(broker, option, side, qty, order_type):
    logger.info(f"Inside symphony_place_order function: Placing order for {option['symbol']} with side {side} and qty {qty}")
    # Validate broker and connection
    if not broker or not broker.interactive_symphony:
        raise ValueError("Invalid broker instance")
      
    loop = 0
    orderplaced = False
    logger.info(f"Placing order for {option['symbol']} with side {side} and qty {qty}")
    while (orderplaced == False and loop < 5):
        loop += 1
        try:
            # Force reinitialize Symphony connection
            broker.interactive_symphony = Symphony_Utility.initialize_interactive_symphony()

            option_ltp = Fyers_Utility.get_ltp_from_symbol(broker.fyers, option["symbol"])
            limitAdjustedPrice = adjusted_limit_price(option_ltp,side) if order_type == "limit" else 0
            exchangeInstrumentID =  option["ExchangeInstrumentId"]
            #print(exchangeInstrumentID)
            #qty = abs(qty)
            logger.info(f"Exchange Instrument ID: {exchangeInstrumentID}")

            order_response = broker.interactive_symphony.place_order(
                exchangeSegment= broker.interactive_symphony.EXCHANGE_NSEFO,
                exchangeInstrumentID= exchangeInstrumentID,
                productType= broker.interactive_symphony.PRODUCT_NRML,
                orderType= broker.interactive_symphony.ORDER_TYPE_LIMIT if order_type == "limit" else broker.interactive_symphony.ORDER_TYPE_MARKET, 
                orderSide= broker.interactive_symphony.TRANSACTION_TYPE_BUY if side == "buy" else broker.interactive_symphony.TRANSACTION_TYPE_SELL,
                timeInForce='IOC',
                disclosedQuantity= 0,
                orderQuantity= qty,
                limitPrice= limitAdjustedPrice,
                stopPrice= 0,
                apiOrderSource= "API",
                orderUniqueIdentifier= "123abc",
                clientID= os.getenv("Interactive_clientID")
            )
            #print(order_response)
            app_order_id = order_response.get('result', {}).get('AppOrderID')  # Extract AppOrderID
            
            # Check if the AppOrderID exists
            if app_order_id:
            # Now check the order status using the AppOrderID
                time.sleep(0.5)  # 500 milliseconds delay
                order_filled, reason = Symphony_Get_Order_Book.get_order_status(broker.interactive_symphony, clientID=os.getenv("Interactive_clientID"), app_order_id=app_order_id)
                if order_filled:
                    orderplaced=True
                    logger.info(f"Order placed successfully: APP Order ID {app_order_id}")
                else:
                    if reason:
                        logger.error(f"Order was not filled. Reason: {reason}")
                    else:
                        print("Order was not filled, but no specific reason provided.") 
            else:
                logger.info("AppOrderID not found in the order response.")             
        except Exception as e:
            logger.error(f"Unexpected error placing order: {e}")    
            continue


        time.sleep(1)

    return 

