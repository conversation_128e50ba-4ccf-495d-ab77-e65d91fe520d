from datetime import datetime, timed<PERSON>ta
#from Fyers_Utility import *
import json, os
import pandas as pd
import logger_config 
from Get_Total_PNL import get_total_pnl
import calendar
from dotenv import load_dotenv
import re

# Get logger
logger = logger_config.get_logger('main_logger')

# Load environment variables from the .env file
load_dotenv()

# Ensure the PNL_Data directory exists
def ensure_pnl_data_directory():
    """Ensure that the PNL_Data directory exists."""
    folder_path = "PNL_Data"
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)
        logger.info(f"Created folder: {folder_path}")
    return folder_path

def get_file_name(current_datetime):
    """Check if the current month's file exists, create it if not."""
    folder_path = ensure_pnl_data_directory()  # Ensure the folder exists

    # Create the file name based on the month and year
    file_name = f"{current_datetime.strftime('%B_%Y')}.csv"
    file_path = os.path.join(folder_path, file_name)

    if current_datetime.day == 1:  # Check if today is the first day of the month
        if not os.path.exists(file_path):  # Check if the file doesn't exist
            # Create an empty CSV file with headers
            header = ["Date", "Start_Time", "Exit_Time", "Daily_P&L", "Monthly_P&L", "Capital", "PNL %", "Monthly_PNL %","Day_High", "Day_Low"]
            pd.DataFrame(columns=header).to_csv(file_path, index=False)
            logger.info(f"Created new month-start file: {file_path}")
        else:
            logger.info(f"Latest month-start file already exists: {file_path}")
    else:
        # For non-month-start days, use the existing file
        if not os.path.exists(file_path):
            # Create the current month's file if it doesn't exist
            header = ["Date", "Start_Time", "Exit_Time", "Daily_P&L", "Monthly_P&L", "Capital", "PNL %", "Monthly_PNL %","Day_High", "Day_Low"]
            pd.DataFrame(columns=header).to_csv(file_path, index=False)
            logger.info(f"Created current month's file: {file_path}")
        else:
            logger.info(f"Using current month's file: {file_name} in folder {file_path}")

    return file_path


def save_data_to_csv(file_name, pnl_df):
    """Append the DataFrame to the CSV file."""
    pnl_df.to_csv(file_name, mode='a', index=False, header=not os.path.exists(file_name))
    logger.info(f"Appended new PnL data \n{pnl_df} \nto \n{file_name}")

def get_cumulative_pnl(file_name, total_pnl):
    try:
        existing_data = pd.read_csv(file_name)
        if not existing_data.empty:
            cumulative_pnl = existing_data["Monthly_P&L"].iloc[-1] + total_pnl
        else:
            cumulative_pnl = total_pnl
    except FileNotFoundError:
        cumulative_pnl = total_pnl
    
    return cumulative_pnl

def get_day_high_low_pnl():
    """Extract day's highest and lowest PNL values from today's log file."""
    current_datetime = datetime.now()  
    current_date = current_datetime.strftime("%d-%m-%Y")        # e.g., 02-07-2025
    month_folder = current_datetime.strftime("%B_%Y")           # e.g., July_2025

    log_file_path = f"logs/{month_folder}/{current_date}.log"  # Correct full path
    
    day_high_pnl = float('-inf')
    day_low_pnl = float('inf')
    
    try:
        with open(log_file_path, 'r') as file:
            log_content = file.read()
            
            # Regex to find values like "Total PNL: 123.45"
            pnl_matches = re.findall(r'Total PNL: ([-]?\d+\.?\d*)', log_content)
            
            if pnl_matches:
                # Convert matches to float and find min/max
                pnl_values = [float(pnl) for pnl in pnl_matches]
                day_high_pnl = max(pnl_values)
                day_low_pnl = min(pnl_values)
                
                print(f"Extracted Day High PNL: {day_high_pnl}, Day Low PNL: {day_low_pnl}")
            else:
                print("No PNL values found in today's log file")
                
    except FileNotFoundError:
        logger.info(f"Log file not found: {log_file_path}")
    except Exception as e:
        logger.info(f"Error reading log file: {str(e)}")
        
    return day_high_pnl, day_low_pnl    

def save_final_pnl(broker, start_time, exit_time):
    capital = float(os.getenv("Capital"))
    current_datetime = datetime.now()
    current_date = current_datetime.strftime("%d-%m-%Y")

    # Get the file name for today (check if file exists, create if not)
    file_name = get_file_name(current_datetime)

    total_pnl, open_pnl, closed_pnl = get_total_pnl(broker)
    cumulative_pnl = get_cumulative_pnl(file_name, total_pnl)

    # Calculate PNL %
    pnl_percentage = round((total_pnl / capital) * 100, 2) if capital != 0 else 0

    # Calculate Monthly PNL %
    monthly_pnl_percentage = round((cumulative_pnl / capital) * 100, 2) if capital != 0 else 0

    # Get day's high and low PNL from logs
    day_high_pnl, day_low_pnl = get_day_high_low_pnl()
    
    # Create a DataFrame from the extracted values including Day High and Low
    pnl_df = pd.DataFrame([[current_date, start_time, exit_time, total_pnl, cumulative_pnl, capital, pnl_percentage, monthly_pnl_percentage, day_high_pnl, day_low_pnl]], 
                          columns=["Date", "Start_Time", "Exit_Time", "Daily_P&L", "Monthly_P&L", "Capital", "PNL %", "Monthly_PNL %", "Day_High", "Day_Low"])
        
    # Save the data to CSV
    save_data_to_csv(file_name, pnl_df)

    return
#---------------------------------------------------------------------------------------------------------------
#fyers = initialize_fyers()
#save_final_pnl(fyers)