from fyers_apiv3 import fyersModel
import logger_config 
from dotenv import load_dotenv
import os 

load_dotenv()

#Get logger
logger = logger_config.get_logger('main_logger')

# Determine sell option prices based on VIX
vix_price_map = {
    9: 14, 10: 15, 11: 16, 12:17, 13: 18, 14: 19, 15:20, 16: 20,
    17: 21, 18: 21, 19: 22, 20: 22, 21: 23
}

GLOBAL_QTY = int(os.getenv('GLOBAL_QTY'))
def initialize_fyers():
    CLIENT_ID = os.getenv('APP_ID')
    ACCESS_TOKEN = os.getenv('ACCESS_TOKEN')

    fyers = fyersModel.FyersModel(client_id=CLIENT_ID, token=ACCESS_TOKEN, is_async=False,log_path="")

    return fyers

def get_nifty_atm_strike(fyers):
    """
    Fetch the current Nifty50 index value and determine the nearest ATM strike price.
    """
    try:
        response = fyers.quotes({"symbols": "NSE:NIFTY50-INDEX"})
        if response.get("s") == "ok":
            nifty_price = response["d"][0]['v']["lp"]
            atm_strike = round(nifty_price / 50) * 50  # Rounding to the nearest 50
            return atm_strike
        else:
            return None
    except Exception as e:
        logger.error(f"Error fetching Nifty50 index: {e}")
        return None
        
def get_ltp_batch(fyers, symbols):
    """
    Fetch LTPs for a list of Fyers symbols in a single batch call.
    Returns a dictionary: {symbol: ltp}
    """
    try:
        symbol_str = ",".join(symbols)
        response = fyers.quotes({"symbols": symbol_str})
        ltp_data = {}
        if response.get("s") == "ok":
            for data in response["d"]:
                symbol = data["n"]
                ltp = data["v"]["lp"]
                ltp_data[symbol] = float(ltp)
        return ltp_data
    except Exception as e:
        print(f"Error in batch LTP fetch: {e}")
        return {}            

def get_ltp_from_symbol(fyers, symbol):
    """
    Fetch the current Nifty50 index value and determine the nearest ATM strike price.
    """
    try:
        response = fyers.quotes({"symbols": symbol})
        if response.get("s") == "ok":
            ltp = response["d"][0]['v']["lp"]
            return float(ltp)
        else:
            return None
    except Exception as e:
        logger.error(f"Error fetching Nifty50 index: {e}")
        return None

def get_sell_option_price(vix):
    vix = int(vix)
    #price = 35
    if vix <=8:
        price = 12
    elif vix >= 22:
        price = 24
    else:
        price = vix_price_map.get(vix) # Use vix_price_map for vix values between 9 and 21
    return float(price)

def get_calender_diff_value(price, vix):
    vix = int(vix)
    if vix <= 13:
        diff = 10
    elif 14 <= vix <= 15:
        diff = 8
    elif 16 <= vix <= 18:
        diff = 5
    elif 19 <= vix <= 21:
        diff = 0
    elif vix >= 22:
        diff = -5
    return price + diff

def find_closest_price(target_price, options_dict):
    """
    Given a target price and a dictionary of symbols with their prices, return the symbol and price with the closest match.
    """
    closest_symbol = min(options_dict, key=lambda x: abs(float(options_dict[x]) - float(target_price)))
   
    return {"symbol": closest_symbol, "ltp": options_dict[closest_symbol]}

